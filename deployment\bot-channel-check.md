# Azure Bot Service Teams Channel Configuration

## Check Bot Service Configuration

1. **Go to Azure Portal** → Your Bot Service (`accuremd-bot` or similar)

2. **Check Channels Configuration**:
   - Navigate to "Channels" in the left menu
   - Ensure "Microsoft Teams" channel is enabled
   - If not enabled, click "Microsoft Teams" and configure it

3. **Verify Bot Endpoint**:
   - Go to "Configuration" in the left menu
   - Ensure "Messaging endpoint" is set to: `https://accuremd.azurewebsites.net/api/messages`
   - Ensure "Microsoft App ID" matches: `24a397f4-16dd-4dae-8b8f-5368c3a81fed`

4. **Test Bot Endpoint**:
   - In Bot Service, go to "Test in Web Chat"
   - Send a test message to verify the bot responds

## Alternative: Create Minimal Test Manifest

If the issue persists, try this minimal manifest for testing:

```json
{
  "$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json",
  "manifestVersion": "1.16",
  "version": "1.0.0",
  "id": "2dbb7f02-8f0e-4884-9518-2f4a6b143dbf",
  "developer": {
    "name": "AccureMD",
    "websiteUrl": "https://accuremd.azurewebsites.net",
    "privacyUrl": "https://accuremd.azurewebsites.net/privacy",
    "termsOfUseUrl": "https://accuremd.azurewebsites.net/terms"
  },
  "icons": {
    "color": "color.png",
    "outline": "outline.png"
  },
  "name": {
    "short": "AccureMD",
    "full": "AccureMD Medical Intelligence"
  },
  "description": {
    "short": "Real-time meeting transcription for Microsoft Teams",
    "full": "AccureMD provides real-time transcription capabilities for Microsoft Teams meetings."
  },
  "accentColor": "#FFFFFF",
  "bots": [
    {
      "botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed",
      "scopes": [
        "personal",
        "team"
      ],
      "supportsFiles": false,
      "isNotificationOnly": false
    }
  ],
  "permissions": [
    "identity",
    "messageTeamMembers"
  ],
  "validDomains": [
    "accuremd.azurewebsites.net"
  ]
}
```

## Troubleshooting Commands

```bash
# Check if bot service exists
az bot show --name accuremd-bot --resource-group accuremd-rg

# Check bot channels
az bot show --name accuremd-bot --resource-group accuremd-rg --query "properties.enabledChannels"

# Update bot endpoint if needed
az bot update --name accuremd-bot --resource-group accuremd-rg --endpoint "https://accuremd.azurewebsites.net/api/messages"
```
