using AccureMD.Core.Interfaces;
using AccureMD.Core.Models;
using AccureMD.Services.Implementation;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using Xunit;

namespace AccureMD.Tests;

public class MeetingJoinTests
{
    private readonly Mock<ILogger<MeetingService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IGraphService> _mockGraphService;
    private readonly MeetingService _meetingService;

    public MeetingJoinTests()
    {
        _mockLogger = new Mock<ILogger<MeetingService>>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockGraphService = new Mock<IGraphService>();
        
        _meetingService = new MeetingService(
            _mockLogger.Object,
            _mockConfiguration.Object,
            _mockGraphService.Object);
    }

    [Fact]
    public async Task JoinMeetingAsync_WithValidUrl_ShouldReturnMeetingInfo()
    {
        // Arrange
        var joinUrl = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_test123";
        var expectedCall = new Microsoft.Graph.Models.Call
        {
            Id = "test-call-id",
            State = Microsoft.Graph.Models.CallState.Established
        };

        _mockGraphService
            .Setup(x => x.JoinMeetingAsync(joinUrl, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCall);

        // Act
        var result = await _meetingService.JoinMeetingAsync(joinUrl);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("test-call-id", result.CallId);
        Assert.Equal(MeetingStatus.InProgress, result.Status);
        Assert.Equal(joinUrl, result.JoinUrl);
    }

    [Fact]
    public async Task JoinMeetingFromContextAsync_WithTeamsMeetingActivity_ShouldReturnMeetingInfo()
    {
        // Arrange
        var activity = CreateTeamsMeetingActivity();
        var expectedCall = new Microsoft.Graph.Models.Call
        {
            Id = "context-call-id",
            State = Microsoft.Graph.Models.CallState.Established
        };

        _mockGraphService
            .Setup(x => x.JoinMeetingAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCall);

        // Act
        var result = await _meetingService.JoinMeetingFromContextAsync(activity);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("context-call-id", result.CallId);
        Assert.Equal(MeetingStatus.InProgress, result.Status);
    }

    [Fact]
    public async Task JoinMeetingAsync_AlreadyJoined_ShouldReturnExistingMeeting()
    {
        // Arrange
        var joinUrl = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_test123";
        var expectedCall = new Microsoft.Graph.Models.Call
        {
            Id = "test-call-id",
            State = Microsoft.Graph.Models.CallState.Established
        };

        _mockGraphService
            .Setup(x => x.JoinMeetingAsync(joinUrl, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCall);

        // Act - Join the meeting twice
        var firstResult = await _meetingService.JoinMeetingAsync(joinUrl);
        var secondResult = await _meetingService.JoinMeetingAsync(joinUrl);

        // Assert
        Assert.NotNull(firstResult);
        Assert.NotNull(secondResult);
        Assert.Equal(firstResult.MeetingId, secondResult.MeetingId);
        Assert.Equal(firstResult.CallId, secondResult.CallId);
        
        // Verify GraphService was only called once
        _mockGraphService.Verify(
            x => x.JoinMeetingAsync(joinUrl, It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task LeaveMeetingAsync_WithValidMeetingId_ShouldCallGraphService()
    {
        // Arrange
        var joinUrl = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_test123";
        var expectedCall = new Microsoft.Graph.Models.Call
        {
            Id = "test-call-id",
            State = Microsoft.Graph.Models.CallState.Established
        };

        _mockGraphService
            .Setup(x => x.JoinMeetingAsync(joinUrl, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCall);

        // First join the meeting
        var meetingInfo = await _meetingService.JoinMeetingAsync(joinUrl);

        // Act - Leave the meeting
        await _meetingService.LeaveMeetingAsync(meetingInfo.MeetingId);

        // Assert
        _mockGraphService.Verify(
            x => x.LeaveMeetingAsync("test-call-id", It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Theory]
    [InlineData("https://teams.microsoft.com/l/meetup-join/19%3ameeting_test123")]
    [InlineData("https://teams.microsoft.com/l/meetup-join/19:meeting_test456")]
    [InlineData("https://teams.live.com/meet/test789")]
    public async Task JoinMeetingAsync_WithDifferentUrlFormats_ShouldWork(string joinUrl)
    {
        // Arrange
        var expectedCall = new Microsoft.Graph.Models.Call
        {
            Id = "test-call-id",
            State = Microsoft.Graph.Models.CallState.Established
        };

        _mockGraphService
            .Setup(x => x.JoinMeetingAsync(joinUrl, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCall);

        // Act
        var result = await _meetingService.JoinMeetingAsync(joinUrl);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(joinUrl, result.JoinUrl);
        Assert.Equal(MeetingStatus.InProgress, result.Status);
    }

    private static Activity CreateTeamsMeetingActivity()
    {
        var channelData = new
        {
            meeting = new
            {
                id = "test-meeting-id",
                joinUrl = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_context_test",
                organizer = "test-organizer-id"
            },
            tenant = new
            {
                id = "test-tenant-id"
            },
            eventType = "meeting.started"
        };

        return new Activity
        {
            Type = ActivityTypes.Invoke,
            ChannelId = "msteams",
            ChannelData = JsonSerializer.SerializeToElement(channelData),
            Conversation = new ConversationAccount
            {
                Id = "test-conversation-id"
            }
        };
    }
}
