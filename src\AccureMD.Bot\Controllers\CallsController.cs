using AccureMD.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccureMD.Bot.Controllers;

/// <summary>
/// Controller for handling Microsoft Graph Communications API callbacks
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CallsController : ControllerBase
{
    private readonly ILogger<CallsController> _logger;
    private readonly IMeetingService _meetingService;

    public CallsController(
        ILogger<CallsController> logger,
        IMeetingService meetingService)
    {
        _logger = logger;
        _meetingService = meetingService;
    }

    /// <summary>
    /// Handle call notifications from Microsoft Graph
    /// </summary>
    [HttpPost("callback")]
    public async Task<IActionResult> HandleCallNotification([FromBody] JsonElement notification)
    {
        try
        {
            _logger.LogInformation("Received call notification: {Notification}", notification.ToString());

            // Process the notification
            await ProcessCallNotificationAsync(notification);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing call notification");
            return StatusCode(500, "Error processing notification");
        }
    }

    /// <summary>
    /// Handle call state changes and events
    /// </summary>
    [HttpPost("events")]
    public async Task<IActionResult> HandleCallEvents([FromBody] JsonElement eventData)
    {
        try
        {
            _logger.LogInformation("Received call event: {EventData}", eventData.ToString());

            // Process the event
            await ProcessCallEventAsync(eventData);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing call event");
            return StatusCode(500, "Error processing event");
        }
    }

    private async Task ProcessCallNotificationAsync(JsonElement notification)
    {
        try
        {
            // Extract call information from notification
            if (notification.TryGetProperty("value", out var valueArray) && valueArray.ValueKind == JsonValueKind.Array)
            {
                foreach (var item in valueArray.EnumerateArray())
                {
                    if (item.TryGetProperty("resourceData", out var resourceData))
                    {
                        var callId = resourceData.TryGetProperty("id", out var idElement) 
                            ? idElement.GetString() : null;
                        
                        var state = resourceData.TryGetProperty("state", out var stateElement) 
                            ? stateElement.GetString() : null;

                        if (!string.IsNullOrEmpty(callId))
                        {
                            _logger.LogInformation("Call {CallId} state changed to: {State}", callId, state);
                            
                            // Handle call state changes
                            await HandleCallStateChangeAsync(callId, state);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing call notification");
        }
    }

    private async Task ProcessCallEventAsync(JsonElement eventData)
    {
        try
        {
            // Process call events like participant changes, media events, etc.
            var eventType = eventData.TryGetProperty("eventType", out var typeElement) 
                ? typeElement.GetString() : null;

            var callId = eventData.TryGetProperty("callId", out var callIdElement) 
                ? callIdElement.GetString() : null;

            _logger.LogInformation("Processing call event - Type: {EventType}, CallId: {CallId}", 
                eventType, callId);

            switch (eventType)
            {
                case "participantJoined":
                    await HandleParticipantJoinedAsync(callId, eventData);
                    break;
                case "participantLeft":
                    await HandleParticipantLeftAsync(callId, eventData);
                    break;
                case "mediaReceived":
                    await HandleMediaReceivedAsync(callId, eventData);
                    break;
                default:
                    _logger.LogDebug("Unhandled call event type: {EventType}", eventType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing call event");
        }
    }

    private async Task HandleCallStateChangeAsync(string? callId, string? state)
    {
        if (string.IsNullOrEmpty(callId))
            return;

        try
        {
            _logger.LogInformation("Handling call state change - CallId: {CallId}, State: {State}", callId, state);

            // Update meeting status based on call state
            switch (state?.ToLowerInvariant())
            {
                case "established":
                    _logger.LogInformation("Call established successfully: {CallId}", callId);
                    break;
                case "terminated":
                    _logger.LogInformation("Call terminated: {CallId}", callId);
                    break;
                case "incoming":
                    _logger.LogInformation("Incoming call: {CallId}", callId);
                    break;
                default:
                    _logger.LogDebug("Call state: {State} for call: {CallId}", state, callId);
                    break;
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling call state change for call: {CallId}", callId);
        }
    }

    private async Task HandleParticipantJoinedAsync(string? callId, JsonElement eventData)
    {
        try
        {
            _logger.LogInformation("Participant joined call: {CallId}", callId);
            // TODO: Update meeting participants
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling participant joined event");
        }
    }

    private async Task HandleParticipantLeftAsync(string? callId, JsonElement eventData)
    {
        try
        {
            _logger.LogInformation("Participant left call: {CallId}", callId);
            // TODO: Update meeting participants
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling participant left event");
        }
    }

    private async Task HandleMediaReceivedAsync(string? callId, JsonElement eventData)
    {
        try
        {
            _logger.LogDebug("Media received for call: {CallId}", callId);
            // TODO: Process audio data for transcription
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling media received event");
        }
    }
}
