using AccureMD.Core.Models;
using Microsoft.Bot.Schema;

namespace AccureMD.Core.Interfaces;

/// <summary>
/// Interface for Microsoft Teams meeting services
/// </summary>
public interface IMeetingService
{
    /// <summary>
    /// Joins a Teams meeting
    /// </summary>
    /// <param name="joinUrl">The meeting join URL</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Meeting information</returns>
    Task<MeetingInfo> JoinMeetingAsync(
        string joinUrl,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Joins a Teams meeting from meeting context
    /// </summary>
    /// <param name="activity">Teams activity with meeting context</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Meeting information</returns>
    Task<MeetingInfo> JoinMeetingFromContextAsync(
        Activity activity,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Leaves a Teams meeting
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task completion</returns>
    Task LeaveMeetingAsync(
        string meetingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets meeting information
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Meeting information</returns>
    Task<MeetingInfo?> GetMeetingInfoAsync(
        string meetingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current meeting participants
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of meeting participants</returns>
    Task<List<MeetingParticipant>> GetMeetingParticipantsAsync(
        string meetingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the bot has permission to record the meeting
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if recording is allowed</returns>
    Task<bool> CanRecordMeetingAsync(
        string meetingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Requests permission to record the meeting
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if permission was granted</returns>
    Task<bool> RequestRecordingPermissionAsync(
        string meetingId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Event fired when meeting status changes
    /// </summary>
    event EventHandler<MeetingStatusEventArgs> MeetingStatusChanged;

    /// <summary>
    /// Event fired when participants join or leave
    /// </summary>
    event EventHandler<ParticipantEventArgs> ParticipantChanged;
}

/// <summary>
/// Event arguments for meeting status events
/// </summary>
public class MeetingStatusEventArgs : EventArgs
{
    public string MeetingId { get; set; } = string.Empty;
    public MeetingStatus Status { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Event arguments for participant events
/// </summary>
public class ParticipantEventArgs : EventArgs
{
    public string MeetingId { get; set; } = string.Empty;
    public MeetingParticipant Participant { get; set; } = new();
    public ParticipantAction Action { get; set; }
}

/// <summary>
/// Participant action enumeration
/// </summary>
public enum ParticipantAction
{
    Joined,
    Left,
    Updated
}
