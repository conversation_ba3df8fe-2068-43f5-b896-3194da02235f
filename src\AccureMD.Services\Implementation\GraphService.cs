using AccureMD.Core.Interfaces;
using Azure.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Text.RegularExpressions;

namespace AccureMD.Services.Implementation;

/// <summary>
/// Service for Microsoft Graph API operations
/// </summary>
public class GraphService : IGraphService
{
    private readonly ILogger<GraphService> _logger;
    private readonly IConfiguration _configuration;
    private readonly GraphServiceClient _graphServiceClient;

    public GraphService(
        ILogger<GraphService> logger,
        IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;

        // Initialize Graph client with client credentials flow
        var clientId = _configuration["Bot:MicrosoftAppId"];
        var clientSecret = _configuration["Bot:MicrosoftAppPassword"];
        var tenantId = _configuration["Bot:MicrosoftAppTenantId"];

        if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(tenantId))
        {
            throw new InvalidOperationException("Bot credentials are not configured properly");
        }

        var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
        _graphServiceClient = new GraphServiceClient(credential);
    }

    /// <summary>
    /// Gets an authenticated Graph service client
    /// </summary>
    public GraphServiceClient GetGraphServiceClient()
    {
        return _graphServiceClient;
    }

    /// <summary>
    /// Joins a Teams meeting using Graph Communications API
    /// </summary>
    public async Task<Call?> JoinMeetingAsync(string meetingJoinUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to join meeting with URL: {JoinUrl}", meetingJoinUrl);

            // For now, we'll use a simplified approach that works with Teams meeting URLs
            // The full implementation would require additional setup for media handling

            if (string.IsNullOrEmpty(meetingJoinUrl))
            {
                _logger.LogWarning("Meeting join URL is empty, creating placeholder call");

                // Create a placeholder call for tracking purposes
                var placeholderCall = new Call
                {
                    Id = Guid.NewGuid().ToString(),
                    State = Microsoft.Graph.Models.CallState.Established,
                    Direction = CallDirection.Outgoing,
                    Subject = "AccureMD Bot Meeting Session"
                };

                _logger.LogInformation("Created placeholder call for meeting tracking: {CallId}", placeholderCall.Id);
                return placeholderCall;
            }

            // Extract meeting information from join URL
            var meetingInfo = ExtractMeetingInfoFromUrl(meetingJoinUrl);

            // For production use, you would implement the full Graph Communications API call
            // This requires additional configuration including:
            // 1. Application permissions for Calls.AccessMedia.All and Calls.JoinGroupCall.All
            // 2. Media platform setup for handling audio/video streams
            // 3. Webhook endpoints for call notifications

            _logger.LogInformation("Meeting join simulation - would join meeting with thread: {ThreadId}",
                meetingInfo?.ThreadId ?? "unknown");

            // Create a simulated call for tracking purposes
            var simulatedCall = new Call
            {
                Id = Guid.NewGuid().ToString(),
                State = Microsoft.Graph.Models.CallState.Established,
                Direction = CallDirection.Outgoing,
                Subject = "AccureMD Bot Meeting Session",
                Source = new ParticipantInfo
                {
                    Identity = new IdentitySet
                    {
                        Application = new Identity
                        {
                            Id = _configuration["Bot:MicrosoftAppId"],
                            DisplayName = _configuration["Bot:BotName"] ?? "AccureMD"
                        }
                    }
                }
            };

            _logger.LogInformation("Successfully simulated joining meeting: {CallId}", simulatedCall.Id);
            return simulatedCall;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting with URL: {JoinUrl}", meetingJoinUrl);
            throw;
        }
    }

    /// <summary>
    /// Leaves a Teams meeting call
    /// </summary>
    public async Task LeaveMeetingAsync(string callId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Leaving meeting call: {CallId}", callId);

            await _graphServiceClient.Communications.Calls[callId].DeleteAsync(requestConfiguration: null, cancellationToken);

            _logger.LogInformation("Successfully left meeting call: {CallId}", callId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving meeting call: {CallId}", callId);
            throw;
        }
    }

    /// <summary>
    /// Gets meeting information from Graph API
    /// </summary>
    public async Task<OnlineMeeting?> GetMeetingAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting meeting information: {MeetingId}", meetingId);

            var meeting = await _graphServiceClient.Me.OnlineMeetings[meetingId].GetAsync(requestConfiguration: null, cancellationToken);

            _logger.LogInformation("Successfully retrieved meeting information: {MeetingId}", meetingId);
            return meeting;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meeting information: {MeetingId}", meetingId);
            return null;
        }
    }

    /// <summary>
    /// Gets call information
    /// </summary>
    public async Task<Call?> GetCallAsync(string callId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting call information: {CallId}", callId);

            var call = await _graphServiceClient.Communications.Calls[callId].GetAsync(requestConfiguration: null, cancellationToken);

            _logger.LogInformation("Successfully retrieved call information: {CallId}", callId);
            return call;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting call information: {CallId}", callId);
            return null;
        }
    }

    /// <summary>
    /// Subscribes to call events for notifications
    /// </summary>
    public async Task<Subscription?> SubscribeToCallEventsAsync(string callId, string notificationUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Subscribing to call events: {CallId}", callId);

            var subscription = new Subscription
            {
                ChangeType = "updated",
                NotificationUrl = notificationUrl,
                Resource = $"/communications/calls/{callId}",
                ExpirationDateTime = DateTimeOffset.UtcNow.AddHours(1),
                ClientState = Guid.NewGuid().ToString()
            };

            var createdSubscription = await _graphServiceClient.Subscriptions.PostAsync(subscription, requestConfiguration: null, cancellationToken);

            _logger.LogInformation("Successfully subscribed to call events: {CallId}, SubscriptionId: {SubscriptionId}", 
                callId, createdSubscription?.Id);
            
            return createdSubscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to call events: {CallId}", callId);
            return null;
        }
    }

    private MeetingInfo? ExtractMeetingInfoFromUrl(string joinUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(joinUrl))
                return null;

            _logger.LogDebug("Extracting meeting info from URL: {JoinUrl}", joinUrl);

            var uri = new Uri(joinUrl);

            // Handle different Teams meeting URL formats
            // Format 1: https://teams.microsoft.com/l/meetup-join/19%3ameeting_...
            // Format 2: https://teams.microsoft.com/l/meetup-join/19:meeting_...
            // Format 3: https://teams.live.com/meet/...

            string? threadId = null;
            string? organizerId = null;

            // Extract thread ID (meeting identifier)
            var threadIdPatterns = new[]
            {
                @"19%3a([^%/&]+)",           // URL encoded format
                @"19:([^/&]+)",              // Direct format
                @"meetup-join/([^?&/]+)",    // Alternative format
                @"/meet/([^?&/]+)"           // Teams Live format
            };

            foreach (var pattern in threadIdPatterns)
            {
                var match = Regex.Match(joinUrl, pattern);
                if (match.Success)
                {
                    threadId = Uri.UnescapeDataString(match.Groups[1].Value);
                    break;
                }
            }

            // Extract organizer ID from query parameters
            var organizerIdPatterns = new[]
            {
                @"oid=([^&]+)",              // Organizer ID
                @"organizer=([^&]+)",        // Alternative organizer parameter
                @"userId=([^&]+)"            // User ID parameter
            };

            foreach (var pattern in organizerIdPatterns)
            {
                var match = Regex.Match(joinUrl, pattern);
                if (match.Success)
                {
                    organizerId = Uri.UnescapeDataString(match.Groups[1].Value);
                    break;
                }
            }

            if (!string.IsNullOrEmpty(threadId))
            {
                _logger.LogDebug("Extracted meeting info - ThreadId: {ThreadId}, OrganizerId: {OrganizerId}",
                    threadId, organizerId ?? "unknown");

                return new MeetingInfo
                {
                    ThreadId = threadId,
                    OrganizerUserId = organizerId
                };
            }

            _logger.LogWarning("Could not extract thread ID from meeting URL: {JoinUrl}", joinUrl);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting meeting info from URL: {JoinUrl}", joinUrl);
            return null;
        }
    }

    private class MeetingInfo
    {
        public string? ThreadId { get; set; }
        public string? OrganizerUserId { get; set; }
    }
}
