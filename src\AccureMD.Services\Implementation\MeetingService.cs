using AccureMD.Core.Interfaces;
using AccureMD.Core.Models;
using Microsoft.Bot.Schema;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using CoreMeetingInfo = AccureMD.Core.Models.MeetingInfo;

namespace AccureMD.Services.Implementation;

/// <summary>
/// Service for managing Microsoft Teams meeting interactions
/// </summary>
public class MeetingService : IMeetingService
{
    private readonly ILogger<MeetingService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IGraphService _graphService;
    private readonly ConcurrentDictionary<string, CoreMeetingInfo> _activeMeetings;

    public event EventHandler<MeetingStatusEventArgs>? MeetingStatusChanged;
    public event EventHandler<ParticipantEventArgs>? ParticipantChanged;

    public MeetingService(
        ILogger<MeetingService> logger,
        IConfiguration configuration,
        IGraphService graphService)
    {
        _logger = logger;
        _configuration = configuration;
        _graphService = graphService;
        _activeMeetings = new ConcurrentDictionary<string, CoreMeetingInfo>();
    }

    /// <summary>
    /// Joins a Teams meeting using the provided join URL
    /// </summary>
    public async Task<CoreMeetingInfo> JoinMeetingAsync(string joinUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to join meeting with URL: {JoinUrl}", joinUrl);

            // Extract meeting ID from join URL
            var meetingId = ExtractMeetingIdFromUrl(joinUrl);
            
            // Check if already in this meeting
            if (_activeMeetings.TryGetValue(meetingId, out var existingMeetingInfo))
            {
                _logger.LogInformation("Already joined meeting: {MeetingId}", meetingId);
                return existingMeetingInfo;
            }

            // Create meeting info object
            var meetingInfo = new CoreMeetingInfo
            {
                MeetingId = meetingId,
                JoinUrl = joinUrl,
                Status = MeetingStatus.InProgress,
                StartTime = DateTime.UtcNow
            };

            // TODO: Implement actual Graph API call to join meeting
            // This is a placeholder for the actual implementation
            await JoinMeetingViaGraphAsync(meetingInfo, cancellationToken);

            // Store active meeting
            _activeMeetings[meetingId] = meetingInfo;

            // Raise event
            MeetingStatusChanged?.Invoke(this, new MeetingStatusEventArgs
            {
                MeetingId = meetingId,
                Status = MeetingStatus.InProgress
            });

            _logger.LogInformation("Successfully joined meeting: {MeetingId}", meetingId);
            return meetingInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join meeting with URL: {JoinUrl}", joinUrl);
            throw;
        }
    }

    /// <summary>
    /// Joins a Teams meeting from meeting context
    /// </summary>
    public async Task<CoreMeetingInfo> JoinMeetingFromContextAsync(Activity activity, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Attempting to join meeting from activity context");

            // Extract meeting information from Teams activity
            var meetingInfo = ExtractMeetingInfoFromActivity(activity);
            if (meetingInfo == null)
            {
                throw new InvalidOperationException("Unable to extract meeting information from activity context");
            }

            // Check if already in this meeting
            if (_activeMeetings.TryGetValue(meetingInfo.MeetingId, out var existingMeeting))
            {
                _logger.LogInformation("Already joined meeting: {MeetingId}", meetingInfo.MeetingId);
                return existingMeeting;
            }

            // Join the meeting using Graph API
            await JoinMeetingViaGraphAsync(meetingInfo, cancellationToken);

            // Store active meeting
            _activeMeetings[meetingInfo.MeetingId] = meetingInfo;

            // Raise event
            MeetingStatusChanged?.Invoke(this, new MeetingStatusEventArgs
            {
                MeetingId = meetingInfo.MeetingId,
                Status = MeetingStatus.InProgress
            });

            _logger.LogInformation("Successfully joined meeting from context: {MeetingId}", meetingInfo.MeetingId);
            return meetingInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join meeting from activity context");
            throw;
        }
    }

    /// <summary>
    /// Leaves a Teams meeting
    /// </summary>
    public async Task LeaveMeetingAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Leaving meeting: {MeetingId}", meetingId);

            if (_activeMeetings.TryRemove(meetingId, out var meetingInfo))
            {
                meetingInfo.Status = MeetingStatus.Completed;
                meetingInfo.EndTime = DateTime.UtcNow;

                // TODO: Implement actual Graph API call to leave meeting
                await LeaveMeetingViaGraphAsync(meetingInfo, cancellationToken);

                // Raise event
                MeetingStatusChanged?.Invoke(this, new MeetingStatusEventArgs
                {
                    MeetingId = meetingId,
                    Status = MeetingStatus.Completed
                });

                _logger.LogInformation("Successfully left meeting: {MeetingId}", meetingId);
            }
            else
            {
                _logger.LogWarning("Attempted to leave meeting that was not active: {MeetingId}", meetingId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave meeting: {MeetingId}", meetingId);
            throw;
        }
    }

    /// <summary>
    /// Gets meeting information
    /// </summary>
    public async Task<CoreMeetingInfo?> GetMeetingInfoAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_activeMeetings.TryGetValue(meetingId, out var meetingInfo))
            {
                // Update with latest information from Graph API
                await UpdateMeetingInfoAsync(meetingInfo, cancellationToken);
                return meetingInfo;
            }

            // Try to get meeting info from Graph API
            return await GetMeetingInfoFromGraphAsync(meetingId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get meeting info: {MeetingId}", meetingId);
            return null;
        }
    }

    /// <summary>
    /// Gets current meeting participants
    /// </summary>
    public async Task<List<MeetingParticipant>> GetMeetingParticipantsAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_activeMeetings.TryGetValue(meetingId, out var meetingInfo))
            {
                // Update participants from Graph API
                await UpdateParticipantsAsync(meetingInfo, cancellationToken);
                return meetingInfo.Participants;
            }

            return [];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get meeting participants: {MeetingId}", meetingId);
            return new List<MeetingParticipant>();
        }
    }

    /// <summary>
    /// Checks if the bot has permission to record the meeting
    /// </summary>
    public async Task<bool> CanRecordMeetingAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement actual permission check via Graph API
            // This is a placeholder implementation
            _logger.LogInformation("Checking recording permissions for meeting: {MeetingId}", meetingId);
            
            // For now, return true as placeholder
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check recording permissions: {MeetingId}", meetingId);
            return false;
        }
    }

    /// <summary>
    /// Requests permission to record the meeting
    /// </summary>
    public async Task<bool> RequestRecordingPermissionAsync(string meetingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Requesting recording permission for meeting: {MeetingId}", meetingId);
            
            // TODO: Implement actual permission request via Graph API
            // This would typically involve sending a notification to meeting organizer
            
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to request recording permission: {MeetingId}", meetingId);
            return false;
        }
    }



    private static string ExtractMeetingIdFromUrl(string joinUrl)
    {
        // Generate a unique meeting ID based on URL
        return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(joinUrl))
            .Replace("+", "-")
            .Replace("/", "_")
            .Replace("=", "")[..16];
    }

    private CoreMeetingInfo? ExtractMeetingInfoFromActivity(Activity activity)
    {
        try
        {
            _logger.LogDebug("Extracting meeting info from activity - Channel: {Channel}, Type: {Type}",
                activity.ChannelId, activity.Type);

            // Check if this is a Teams meeting context
            if (activity.ChannelData != null)
            {
                var channelData = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(
                    activity.ChannelData.ToString() ?? "{}");

                _logger.LogDebug("Channel data: {ChannelData}", channelData.ToString());

                // Try to extract meeting information from channel data
                if (channelData.TryGetProperty("meeting", out var meetingElement))
                {
                    var meetingId = meetingElement.TryGetProperty("id", out var idElement)
                        ? idElement.GetString() : null;

                    var joinUrl = meetingElement.TryGetProperty("joinUrl", out var joinUrlElement)
                        ? joinUrlElement.GetString() : null;

                    var organizer = meetingElement.TryGetProperty("organizer", out var organizerElement)
                        ? organizerElement.GetString() : null;

                    if (!string.IsNullOrEmpty(meetingId))
                    {
                        _logger.LogInformation("Found meeting in channel data - ID: {MeetingId}, JoinUrl: {HasJoinUrl}",
                            meetingId, !string.IsNullOrEmpty(joinUrl));

                        return new CoreMeetingInfo
                        {
                            MeetingId = meetingId,
                            JoinUrl = joinUrl ?? string.Empty,
                            OrganizerId = organizer,
                            Status = MeetingStatus.InProgress,
                            StartTime = DateTime.UtcNow
                        };
                    }
                }

                // Try to extract from Teams-specific properties
                if (channelData.TryGetProperty("tenant", out var tenantElement))
                {
                    var tenantId = tenantElement.TryGetProperty("id", out var tenantIdElement)
                        ? tenantIdElement.GetString() : null;

                    // Check for Teams meeting context indicators
                    var isTeamsMeeting = channelData.TryGetProperty("eventType", out var eventTypeElement) &&
                                        eventTypeElement.GetString()?.Contains("meeting") == true;

                    if (isTeamsMeeting || activity.ChannelId == "msteams")
                    {
                        // Generate a meeting ID based on conversation and tenant
                        if (!string.IsNullOrEmpty(tenantId) && !string.IsNullOrEmpty(activity.Conversation?.Id))
                        {
                            var meetingId = GenerateMeetingIdFromContext(tenantId, activity.Conversation.Id);

                            _logger.LogInformation("Generated meeting ID from context: {MeetingId}", meetingId);

                            return new CoreMeetingInfo
                            {
                                MeetingId = meetingId,
                                JoinUrl = string.Empty,
                                Status = MeetingStatus.InProgress,
                                StartTime = DateTime.UtcNow
                            };
                        }
                    }
                }

                // Check for Teams meeting URL in activity text or attachments
                var extractedJoinUrl = ExtractJoinUrlFromActivity(activity);
                if (!string.IsNullOrEmpty(extractedJoinUrl))
                {
                    var meetingId = ExtractMeetingIdFromUrl(extractedJoinUrl);

                    _logger.LogInformation("Found join URL in activity - MeetingId: {MeetingId}", meetingId);

                    return new CoreMeetingInfo
                    {
                        MeetingId = meetingId,
                        JoinUrl = extractedJoinUrl,
                        Status = MeetingStatus.InProgress,
                        StartTime = DateTime.UtcNow
                    };
                }
            }

            // Fallback: use conversation ID as meeting ID for Teams channels
            if (activity.ChannelId == "msteams" && !string.IsNullOrEmpty(activity.Conversation?.Id))
            {
                var meetingId = $"teams_{activity.Conversation.Id}";

                _logger.LogInformation("Using conversation ID as meeting ID: {MeetingId}", meetingId);

                return new CoreMeetingInfo
                {
                    MeetingId = meetingId,
                    JoinUrl = string.Empty,
                    Status = MeetingStatus.InProgress,
                    StartTime = DateTime.UtcNow
                };
            }

            _logger.LogWarning("Could not extract meeting information from activity");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting meeting info from activity");
            return null;
        }
    }

    private static string GenerateMeetingIdFromContext(string tenantId, string conversationId)
    {
        // Create a deterministic meeting ID from tenant and conversation
        var combined = $"{tenantId}_{conversationId}";
        var hash = System.Security.Cryptography.SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(combined));
        return Convert.ToBase64String(hash)[..16].Replace("+", "-").Replace("/", "_");
    }

    private string? ExtractJoinUrlFromActivity(Activity activity)
    {
        try
        {
            // Check activity text for Teams meeting URLs
            if (!string.IsNullOrEmpty(activity.Text))
            {
                var urlPattern = @"https://teams\.microsoft\.com/l/meetup-join/[^\s]+";
                var match = System.Text.RegularExpressions.Regex.Match(activity.Text, urlPattern);
                if (match.Success)
                {
                    return match.Value;
                }
            }

            // Check attachments for meeting URLs
            if (activity.Attachments != null)
            {
                foreach (var attachment in activity.Attachments)
                {
                    if (attachment.Content != null)
                    {
                        var contentStr = attachment.Content.ToString();
                        if (!string.IsNullOrEmpty(contentStr) && contentStr.Contains("teams.microsoft.com"))
                        {
                            var urlPattern = @"https://teams\.microsoft\.com/l/meetup-join/[^\s""']+";
                            var match = System.Text.RegularExpressions.Regex.Match(contentStr, urlPattern);
                            if (match.Success)
                            {
                                return match.Value;
                            }
                        }
                    }
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting join URL from activity");
            return null;
        }
    }

    private async Task JoinMeetingViaGraphAsync(CoreMeetingInfo meetingInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Joining meeting via Graph API: {MeetingId}", meetingInfo.MeetingId);

            // Use Graph service to join the meeting
            var call = await _graphService.JoinMeetingAsync(meetingInfo.JoinUrl, cancellationToken);

            if (call != null)
            {
                meetingInfo.CallId = call.Id;
                _logger.LogInformation("Successfully joined meeting via Graph API: {MeetingId}, CallId: {CallId}",
                    meetingInfo.MeetingId, call.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join meeting via Graph API: {MeetingId}", meetingInfo.MeetingId);
            throw;
        }
    }

    private async Task LeaveMeetingViaGraphAsync(CoreMeetingInfo meetingInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Leaving meeting via Graph API: {MeetingId}", meetingInfo.MeetingId);

            if (!string.IsNullOrEmpty(meetingInfo.CallId))
            {
                await _graphService.LeaveMeetingAsync(meetingInfo.CallId, cancellationToken);
                _logger.LogInformation("Successfully left meeting via Graph API: {MeetingId}", meetingInfo.MeetingId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave meeting via Graph API: {MeetingId}", meetingInfo.MeetingId);
            throw;
        }
    }

    private async Task UpdateMeetingInfoAsync(CoreMeetingInfo meetingInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Updating meeting info: {MeetingId}", meetingInfo.MeetingId);

            // Update meeting info from Graph API if we have a call ID
            if (!string.IsNullOrEmpty(meetingInfo.CallId))
            {
                var call = await _graphService.GetCallAsync(meetingInfo.CallId, cancellationToken);
                if (call != null)
                {
                    // Update meeting status based on call state
                    meetingInfo.Status = call.State switch
                    {
                        Microsoft.Graph.Models.CallState.Incoming => MeetingStatus.InProgress,
                        Microsoft.Graph.Models.CallState.Establishing => MeetingStatus.InProgress,
                        Microsoft.Graph.Models.CallState.Established => MeetingStatus.InProgress,
                        Microsoft.Graph.Models.CallState.Terminated => MeetingStatus.Completed,
                        _ => meetingInfo.Status
                    };
                }
            }

            meetingInfo.UpdatedAt = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update meeting info: {MeetingId}", meetingInfo.MeetingId);
        }
    }

    private async Task<CoreMeetingInfo?> GetMeetingInfoFromGraphAsync(string meetingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Getting meeting info from Graph API: {MeetingId}", meetingId);

            // Try to get meeting info from Graph API
            var meeting = await _graphService.GetMeetingAsync(meetingId, cancellationToken);

            if (meeting != null)
            {
                return new CoreMeetingInfo
                {
                    MeetingId = meetingId,
                    JoinUrl = meeting.JoinWebUrl ?? string.Empty,
                    Status = MeetingStatus.InProgress,
                    StartTime = meeting.StartDateTime?.DateTime ?? DateTime.UtcNow,
                    EndTime = meeting.EndDateTime?.DateTime
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get meeting info from Graph API: {MeetingId}", meetingId);
            return null;
        }
    }

    private async Task UpdateParticipantsAsync(CoreMeetingInfo meetingInfo, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Updating participants for meeting: {MeetingId}", meetingInfo.MeetingId);

            // TODO: Update participants from Graph API
            // This would involve getting call participants if we have a call ID

            await Task.Delay(10, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update participants: {MeetingId}", meetingInfo.MeetingId);
        }
    }
}
