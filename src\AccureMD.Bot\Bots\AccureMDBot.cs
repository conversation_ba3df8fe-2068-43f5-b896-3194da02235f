using AccureMD.Bot.Configuration;
using AccureMD.Core.Interfaces;
using AccureMD.Core.Models;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Schema;
using Microsoft.Bot.Schema.Teams;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace AccureMD.Bot.Bots;

/// <summary>
/// Main bot implementation for AccureMD Teams extension
/// </summary>
public class AccureMDBot : TeamsActivityHandler
{
    private readonly ILogger<AccureMDBot> _logger;
    private readonly BotConfiguration _config;
    private readonly IMeetingService _meetingService;
    private readonly ITranscriptionService _transcriptionService;
    private readonly IRecordingService _recordingService;

    public AccureMDBot(
        ILogger<AccureMDBot> logger,
        BotConfiguration config,
        IMeetingService meetingService,
        ITranscriptionService transcriptionService,
        IRecordingService recordingService)
    {
        _logger = logger;
        _config = config;
        _meetingService = meetingService;
        _transcriptionService = transcriptionService;
        _recordingService = recordingService;

        // Subscribe to service events
        _transcriptionService.TranscriptionSegmentReceived += OnTranscriptionSegmentReceived;
        _recordingService.RecordingStatusChanged += OnRecordingStatusChanged;
        _meetingService.MeetingStatusChanged += OnMeetingStatusChanged;
    }

    /// <summary>
    /// Handle when the bot is added to a conversation
    /// </summary>
    protected override async Task OnMembersAddedAsync(
        IList<ChannelAccount> membersAdded,
        ITurnContext<IConversationUpdateActivity> turnContext,
        CancellationToken cancellationToken)
    {
        var welcomeText = "Hello! I'm AccureMD, your meeting transcription assistant. " +
                         "I can help you record and transcribe your Teams meetings in real-time.";

        foreach (var member in membersAdded)
        {
            if (member.Id != turnContext.Activity.Recipient.Id)
            {
                await turnContext.SendActivityAsync(
                    MessageFactory.Text(welcomeText),
                    cancellationToken);
            }
        }
    }

    /// <summary>
    /// Handle message activities
    /// </summary>
    protected override async Task OnMessageActivityAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        var text = turnContext.Activity.Text?.Trim().ToLowerInvariant();

        try
        {
            switch (text)
            {
                case "start transcription":
                case "start":
                    await HandleStartTranscriptionAsync(turnContext, cancellationToken);
                    break;

                case "stop transcription":
                case "stop":
                    await HandleStopTranscriptionAsync(turnContext, cancellationToken);
                    break;

                case "start recording":
                    await HandleStartRecordingAsync(turnContext, cancellationToken);
                    break;

                case "stop recording":
                    await HandleStopRecordingAsync(turnContext, cancellationToken);
                    break;

                case "status":
                    await HandleStatusRequestAsync(turnContext, cancellationToken);
                    break;

                case "help":
                default:
                    await HandleHelpRequestAsync(turnContext, cancellationToken);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message: {Message}", text);
            await turnContext.SendActivityAsync(
                MessageFactory.Text("Sorry, I encountered an error processing your request. Please try again."),
                cancellationToken);
        }
    }

    /// <summary>
    /// Handle Teams meeting start events
    /// </summary>
    protected override async Task OnTeamsMeetingStartAsync(
        MeetingStartEventDetails meeting,
        ITurnContext<IEventActivity> turnContext,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Meeting started: {MeetingId}", meeting.Id);

        try
        {
            // Join the meeting automatically
            var meetingInfo = await _meetingService.JoinMeetingAsync(meeting.JoinUrl.ToString(), cancellationToken);
            
            // Send welcome message to meeting chat
            var welcomeMessage = "AccureMD has joined the meeting. " +
                               "Type 'start transcription' to begin real-time transcription.";
            
            await turnContext.SendActivityAsync(
                MessageFactory.Text(welcomeMessage),
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting: {MeetingId}", meeting.Id);
        }
    }

    /// <summary>
    /// Handle Teams meeting end events
    /// </summary>
    protected override async Task OnTeamsMeetingEndAsync(
        MeetingEndEventDetails meeting,
        ITurnContext<IEventActivity> turnContext,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Meeting ended: {MeetingId}", meeting.Id);

        try
        {
            // Stop any active transcription or recording
            await _meetingService.LeaveMeetingAsync(meeting.Id, cancellationToken);
            
            // Send summary message
            var summaryMessage = "Meeting has ended. All transcription and recording activities have been stopped.";
            
            await turnContext.SendActivityAsync(
                MessageFactory.Text(summaryMessage),
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving meeting: {MeetingId}", meeting.Id);
        }
    }

    /// <summary>
    /// Handle compose extension queries
    /// </summary>
    protected override async Task<MessagingExtensionResponse> OnTeamsMessagingExtensionQueryAsync(
        ITurnContext<IInvokeActivity> turnContext,
        MessagingExtensionQuery query,
        CancellationToken cancellationToken)
    {
        var commandId = query.CommandId;

        switch (commandId)
        {
            case "startTranscription":
                return await HandleTranscriptionExtensionAsync(query, cancellationToken);
            default:
                return new MessagingExtensionResponse();
        }
    }

    /// <summary>
    /// Handle Teams task module fetch requests
    /// </summary>
    protected override async Task<TaskModuleResponse> OnTeamsTaskModuleFetchAsync(
        ITurnContext<IInvokeActivity> turnContext,
        TaskModuleRequest taskModuleRequest,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Task module fetch requested");

        try
        {
            // Check if this is a meeting context and auto-join if needed
            await HandleMeetingContextAsync(turnContext, cancellationToken);

            // Return task module for transcription control
            return new TaskModuleResponse
            {
                Task = new TaskModuleContinueResponse
                {
                    Type = "continue",
                    Value = new TaskModuleTaskInfo
                    {
                        Title = "AccureMD - Meeting Transcription",
                        Height = 600,
                        Width = 800,
                        Url = $"{_config.BaseUrl}/static/meeting-sidebar.html"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling task module fetch");

            return new TaskModuleResponse
            {
                Task = new TaskModuleMessageResponse
                {
                    Type = "message",
                    Value = "Sorry, there was an error loading the transcription interface."
                }
            };
        }
    }

    /// <summary>
    /// Handle Teams task module submit requests
    /// </summary>
    protected override async Task<TaskModuleResponse> OnTeamsTaskModuleSubmitAsync(
        ITurnContext<IInvokeActivity> turnContext,
        TaskModuleRequest taskModuleRequest,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Task module submit requested");

        try
        {
            var data = taskModuleRequest.Data as Newtonsoft.Json.Linq.JObject;
            var action = data?["action"]?.ToString();

            switch (action)
            {
                case "startTranscription":
                    await HandleStartTranscriptionFromTaskModuleAsync(turnContext, cancellationToken);
                    break;
                case "stopTranscription":
                    await HandleStopTranscriptionFromTaskModuleAsync(turnContext, cancellationToken);
                    break;
            }

            return new TaskModuleResponse
            {
                Task = new TaskModuleMessageResponse
                {
                    Type = "message",
                    Value = $"Action '{action}' completed successfully."
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling task module submit");

            return new TaskModuleResponse
            {
                Task = new TaskModuleMessageResponse
                {
                    Type = "message",
                    Value = "Sorry, there was an error processing your request."
                }
            };
        }
    }

    /// <summary>
    /// Handle general invoke activities
    /// </summary>
    protected override async Task<InvokeResponse> OnInvokeActivityAsync(
        ITurnContext<IInvokeActivity> turnContext,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Invoke activity received: {Name}", turnContext.Activity.Name);

            // Check if this is a meeting context and auto-join if needed
            await HandleMeetingContextAsync(turnContext, cancellationToken);

            // Handle specific invoke types
            switch (turnContext.Activity.Name)
            {
                case "task/fetch":
                    var taskModuleResponse = await OnTeamsTaskModuleFetchAsync(turnContext,
                        turnContext.Activity.Value as TaskModuleRequest ?? new TaskModuleRequest(),
                        cancellationToken);
                    return CreateInvokeResponse(taskModuleResponse);

                case "task/submit":
                    var submitResponse = await OnTeamsTaskModuleSubmitAsync(turnContext,
                        turnContext.Activity.Value as TaskModuleRequest ?? new TaskModuleRequest(),
                        cancellationToken);
                    return CreateInvokeResponse(submitResponse);

                default:
                    return await base.OnInvokeActivityAsync(turnContext, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling invoke activity: {Name}", turnContext.Activity.Name);
            return CreateInvokeResponse(HttpStatusCode.InternalServerError);
        }
    }

    private async Task HandleStartTranscriptionAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        // Implementation will be added in the services layer
        await turnContext.SendActivityAsync(
            MessageFactory.Text("Starting transcription... (Implementation pending)"),
            cancellationToken);
    }

    private async Task HandleStopTranscriptionAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        // Implementation will be added in the services layer
        await turnContext.SendActivityAsync(
            MessageFactory.Text("Stopping transcription... (Implementation pending)"),
            cancellationToken);
    }

    private async Task HandleStartRecordingAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        // Implementation will be added in the services layer
        await turnContext.SendActivityAsync(
            MessageFactory.Text("Starting recording... (Implementation pending)"),
            cancellationToken);
    }

    private async Task HandleStopRecordingAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        // Implementation will be added in the services layer
        await turnContext.SendActivityAsync(
            MessageFactory.Text("Stopping recording... (Implementation pending)"),
            cancellationToken);
    }

    private async Task HandleStatusRequestAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        var statusMessage = "AccureMD Status:\n" +
                           "- Bot: Active\n" +
                           "- Transcription: Ready\n" +
                           "- Recording: Ready\n" +
                           "- Meeting Integration: Connected";

        await turnContext.SendActivityAsync(
            MessageFactory.Text(statusMessage),
            cancellationToken);
    }

    private async Task HandleHelpRequestAsync(
        ITurnContext<IMessageActivity> turnContext,
        CancellationToken cancellationToken)
    {
        var helpMessage = "AccureMD Commands:\n\n" +
                         "**Transcription:**\n" +
                         "- `start transcription` - Begin real-time transcription\n" +
                         "- `stop transcription` - End transcription\n\n" +
                         "**Recording:**\n" +
                         "- `start recording` - Begin meeting recording\n" +
                         "- `stop recording` - End recording\n\n" +
                         "**Other:**\n" +
                         "- `status` - Check bot status\n" +
                         "- `help` - Show this help message";

        await turnContext.SendActivityAsync(
            MessageFactory.Text(helpMessage),
            cancellationToken);
    }

    private async Task<MessagingExtensionResponse> HandleTranscriptionExtensionAsync(
        MessagingExtensionQuery query,
        CancellationToken cancellationToken)
    {
        // Create adaptive card for transcription settings
        var card = new
        {
            type = "AdaptiveCard",
            version = "1.4",
            body = new object[]
            {
                new
                {
                    type = "TextBlock",
                    text = "Start Transcription",
                    weight = "Bolder",
                    size = "Medium"
                },
                new
                {
                    type = "Input.ChoiceSet",
                    id = "language",
                    label = "Language",
                    value = "en-US",
                    choices = new object[]
                    {
                        new { title = "English (US)", value = "en-US" },
                        new { title = "English (UK)", value = "en-GB" },
                        new { title = "Spanish", value = "es-ES" },
                        new { title = "French", value = "fr-FR" },
                        new { title = "German", value = "de-DE" }
                    }
                }
            },
            actions = new object[]
            {
                new
                {
                    type = "Action.Submit",
                    title = "Start Transcription",
                    data = new { action = "startTranscription" }
                }
            }
        };

        var attachment = new MessagingExtensionAttachment
        {
            ContentType = "application/vnd.microsoft.card.adaptive",
            Content = card
        };

        return new MessagingExtensionResponse
        {
            ComposeExtension = new MessagingExtensionResult
            {
                Type = "result",
                AttachmentLayout = "list",
                Attachments = new[] { attachment }.ToList()
            }
        };
    }

    private void OnTranscriptionSegmentReceived(object? sender, TranscriptionSegmentEventArgs e)
    {
        _logger.LogInformation("Transcription segment received for session: {SessionId}", e.SessionId);
        // Real-time transcription updates will be sent via SignalR
    }

    private void OnRecordingStatusChanged(object? sender, RecordingStatusEventArgs e)
    {
        _logger.LogInformation("Recording status changed: {RecordingId} - {Status}", e.RecordingId, e.Status);
    }

    private void OnMeetingStatusChanged(object? sender, MeetingStatusEventArgs e)
    {
        _logger.LogInformation("Meeting status changed: {MeetingId} - {Status}", e.MeetingId, e.Status);
    }

    /// <summary>
    /// Handle meeting context detection and auto-join
    /// </summary>
    private async Task HandleMeetingContextAsync(ITurnContext turnContext, CancellationToken cancellationToken)
    {
        try
        {
            // Check if this activity is from a Teams meeting context
            if (IsMeetingContext(turnContext.Activity))
            {
                _logger.LogInformation("Meeting context detected, attempting to join meeting");

                // Try to join the meeting using the activity context
                var meetingInfo = await _meetingService.JoinMeetingFromContextAsync(turnContext.Activity, cancellationToken);

                _logger.LogInformation("Successfully joined meeting: {MeetingId}", meetingInfo.MeetingId);

                // Send a welcome message to the meeting
                var welcomeMessage = "AccureMD has joined the meeting and is ready to provide transcription services.";
                await turnContext.SendActivityAsync(MessageFactory.Text(welcomeMessage), cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling meeting context");
            // Don't throw - we want the bot to continue working even if auto-join fails
        }
    }

    /// <summary>
    /// Check if the activity is from a Teams meeting context
    /// </summary>
    private static bool IsMeetingContext(Activity activity)
    {
        // Check for Teams meeting indicators
        if (activity.ChannelId == "msteams" && activity.ChannelData != null)
        {
            try
            {
                var channelData = JsonSerializer.Deserialize<JsonElement>(
                    activity.ChannelData.ToString() ?? "{}");

                // Check for meeting-specific properties
                return channelData.TryGetProperty("meeting", out _) ||
                       channelData.TryGetProperty("eventType", out var eventType) &&
                       eventType.GetString()?.Contains("meeting") == true;
            }
            catch
            {
                // If we can't parse channel data, assume it's not a meeting context
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// Handle start transcription from task module
    /// </summary>
    private async Task HandleStartTranscriptionFromTaskModuleAsync(ITurnContext turnContext, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting transcription from task module");

            // Implementation will be added when transcription service is fully implemented
            await turnContext.SendActivityAsync(
                MessageFactory.Text("Transcription started successfully!"),
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting transcription from task module");
            throw;
        }
    }

    /// <summary>
    /// Handle stop transcription from task module
    /// </summary>
    private async Task HandleStopTranscriptionFromTaskModuleAsync(ITurnContext turnContext, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Stopping transcription from task module");

            // Implementation will be added when transcription service is fully implemented
            await turnContext.SendActivityAsync(
                MessageFactory.Text("Transcription stopped successfully!"),
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping transcription from task module");
            throw;
        }
    }

    /// <summary>
    /// Create an invoke response from a task module response
    /// </summary>
    private static InvokeResponse CreateInvokeResponse(TaskModuleResponse taskModuleResponse)
    {
        return new InvokeResponse
        {
            Status = (int)HttpStatusCode.OK,
            Body = taskModuleResponse
        };
    }

    /// <summary>
    /// Create an invoke response with a specific status code
    /// </summary>
    private static InvokeResponse CreateInvokeResponse(HttpStatusCode statusCode)
    {
        return new InvokeResponse
        {
            Status = (int)statusCode
        };
    }
}
