using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AccureMD.Core.Interfaces;

/// <summary>
/// Interface for Microsoft Graph API operations
/// </summary>
public interface IGraphService
{
    /// <summary>
    /// Gets an authenticated Graph service client
    /// </summary>
    /// <returns>Graph service client</returns>
    GraphServiceClient GetGraphServiceClient();

    /// <summary>
    /// Joins a Teams meeting using Graph Communications API
    /// </summary>
    /// <param name="meetingJoinUrl">The meeting join URL</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Call information</returns>
    Task<Call?> JoinMeetingAsync(string meetingJoinUrl, CancellationToken cancellationToken = default);

    /// <summary>
    /// Leaves a Teams meeting call
    /// </summary>
    /// <param name="callId">The call identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task completion</returns>
    Task LeaveMeetingAsync(string callId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets meeting information from Graph API
    /// </summary>
    /// <param name="meetingId">The meeting identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Meeting information</returns>
    Task<OnlineMeeting?> GetMeetingAsync(string meetingId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets call information
    /// </summary>
    /// <param name="callId">The call identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Call information</returns>
    Task<Call?> GetCallAsync(string callId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribes to call events for notifications
    /// </summary>
    /// <param name="callId">The call identifier</param>
    /// <param name="notificationUrl">URL to receive notifications</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Subscription information</returns>
    Task<Subscription?> SubscribeToCallEventsAsync(string callId, string notificationUrl, CancellationToken cancellationToken = default);
}
