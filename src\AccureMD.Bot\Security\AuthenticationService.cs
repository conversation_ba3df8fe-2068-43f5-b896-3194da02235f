using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace AccureMD.Bot.Security;

/// <summary>
/// Service for handling authentication and authorization
/// </summary>
public class AuthenticationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        IConfiguration configuration,
        ILogger<AuthenticationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Validates a Teams authentication token
    /// </summary>
    /// <param name="token">JWT token from Teams</param>
    /// <returns>Claims principal if valid, null otherwise</returns>
    public async Task<ClaimsPrincipal?> ValidateTeamsTokenAsync(string token)
    {
        try
        {
            var appId = _configuration["Bot:MicrosoftAppId"];
            var tenantId = "common"; // Use "common" for multi-tenant or your tenant ID for single-tenant
            var authority = $"https://login.microsoftonline.com/{tenantId}/v2.0";
            var openIdConfigUrl = $"{authority}/.well-known/openid-configuration";

            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                openIdConfigUrl,
                new OpenIdConnectConfigurationRetriever());
            
            var openIdConfig = await configurationManager.GetConfigurationAsync(CancellationToken.None);

            var tokenHandler = new JwtSecurityTokenHandler();

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuers = new[] { $"https://sts.windows.net/{_configuration["Bot:MicrosoftAppTenantId"]}/", openIdConfig.Issuer },
                ValidAudience = appId,
                IssuerSigningKeys = openIdConfig.SigningKeys,
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Validate the token
            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            
            _logger.LogInformation("Successfully validated Teams token for user: {UserId}", 
                principal.FindFirst("oid")?.Value);
            
            return principal;
        }
        catch (SecurityTokenException ex)
        {
            _logger.LogWarning(ex, "Invalid Teams token provided");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Teams token");
            return null;
        }
    }

    /// <summary>
    /// Validates user permissions for meeting access
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="meetingId">Meeting identifier</param>
    /// <returns>True if user has access to the meeting</returns>
    public async Task<bool> ValidateMeetingAccessAsync(string userId, string meetingId)
    {
        try
        {
            _logger.LogInformation("Validating meeting access for user: {UserId}, meeting: {MeetingId}", 
                userId, meetingId);

            // TODO: Implement actual meeting access validation via Microsoft Graph
            // This would check if the user is a participant in the meeting
            
            // For now, return true as placeholder
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating meeting access for user: {UserId}, meeting: {MeetingId}", 
                userId, meetingId);
            return false;
        }
    }

    /// <summary>
    /// Gets user information from Microsoft Graph
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <returns>User information</returns>
    public async Task<UserInfo?> GetUserInfoAsync(string userId)
    {
        try
        {
            // TODO: Implement actual Graph API call to get user info
            // var user = await _graphClient.Users[userId].Request().GetAsync();
            
            // Placeholder implementation
            return await Task.FromResult(new UserInfo
            {
                Id = userId,
                DisplayName = "User",
                Email = "<EMAIL>",
                Roles = new[] { "Attendee" }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user info for: {UserId}", userId);
            return null;
        }
    }

}

/// <summary>
/// User information model
/// </summary>
public class UserInfo
{
    public string Id { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string[] Roles { get; set; } = Array.Empty<string>();
    public bool IsOrganizer { get; set; }
    public bool IsPresenter { get; set; }
}
