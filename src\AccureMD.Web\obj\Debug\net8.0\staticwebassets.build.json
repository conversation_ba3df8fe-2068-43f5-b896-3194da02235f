{"Version": 1, "Hash": "0HsgMqMicKb++04hrMQtbCKQlQJfjd+t/W4UzxrHLyE=", "Source": "AccureMD.Web", "BasePath": "_content/AccureMD.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AccureMD.Web\\wwwroot", "Source": "AccureMD.Web", "ContentRoot": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\", "BasePath": "_content/AccureMD.Web", "Pattern": "**"}], "Assets": [{"Identity": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\config.html", "SourceId": "AccureMD.Web", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\", "BasePath": "_content/AccureMD.Web", "RelativePath": "static/config#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vgb0ih5k4i", "Integrity": "8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\static\\config.html", "FileLength": 18559, "LastWriteTime": "2025-08-06T20:32:38+00:00"}, {"Identity": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar-legacy.html", "SourceId": "AccureMD.Web", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\", "BasePath": "_content/AccureMD.Web", "RelativePath": "static/meeting-sidebar-legacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q6lm9p6f9d", "Integrity": "Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\static\\meeting-sidebar-legacy.html", "FileLength": 39469, "LastWriteTime": "2025-08-06T20:31:37+00:00"}, {"Identity": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar.html", "SourceId": "AccureMD.Web", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\", "BasePath": "_content/AccureMD.Web", "RelativePath": "static/meeting-sidebar#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i0k7idjahb", "Integrity": "7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\static\\meeting-sidebar.html", "FileLength": 21999, "LastWriteTime": "2025-08-07T07:43:28+00:00"}, {"Identity": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\ui-test.html", "SourceId": "AccureMD.Web", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\", "BasePath": "_content/AccureMD.Web", "RelativePath": "static/ui-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i0k7idjahb", "Integrity": "7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\static\\ui-test.html", "FileLength": 21999, "LastWriteTime": "2025-08-07T18:23:57+00:00"}], "Endpoints": [{"Route": "static/config.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\config.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18559"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:32:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM="}]}, {"Route": "static/config.vgb0ih5k4i.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\config.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18559"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:32:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vgb0ih5k4i"}, {"Name": "label", "Value": "static/config.html"}, {"Name": "integrity", "Value": "sha256-8T85r+BS8Nrgbcc5cYTVC+HylyUPaRkix7ByOnBD8gM="}]}, {"Route": "static/meeting-sidebar-legacy.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar-legacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39469"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos="}]}, {"Route": "static/meeting-sidebar-legacy.q6lm9p6f9d.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar-legacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39469"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 20:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q6lm9p6f9d"}, {"Name": "label", "Value": "static/meeting-sidebar-legacy.html"}, {"Name": "integrity", "Value": "sha256-Ph5yfZrxMn3fm2s6OwtHnjWLftvIngmWT4mv9a3haos="}]}, {"Route": "static/meeting-sidebar.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:43:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}]}, {"Route": "static/meeting-sidebar.i0k7idjahb.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\meeting-sidebar.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 07:43:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i0k7idjahb"}, {"Name": "label", "Value": "static/meeting-sidebar.html"}, {"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}]}, {"Route": "static/ui-test.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\ui-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 18:23:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}]}, {"Route": "static/ui-test.i0k7idjahb.html", "AssetFile": "D:\\iData Project\\AccureMD\\src\\AccureMD.Web\\wwwroot\\static\\ui-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21999"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI=\""}, {"Name": "Last-Modified", "Value": "Thu, 07 Aug 2025 18:23:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i0k7idjahb"}, {"Name": "label", "Value": "static/ui-test.html"}, {"Name": "integrity", "Value": "sha256-7khZlzuAoenx/BezIGok/Q5hPxpiPQ6X3sFQ7cxE5lI="}]}]}