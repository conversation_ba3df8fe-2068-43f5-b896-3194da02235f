{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "2dbb7f02-8f0e-4884-9518-2f4a6b143dbf", "developer": {"name": "AccureMD", "websiteUrl": "https://accuremd.azurewebsites.net", "privacyUrl": "https://accuremd.azurewebsites.net/privacy", "termsOfUseUrl": "https://accuremd.azurewebsites.net/terms"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "AccureMD", "full": "AccureMD Medical Intelligence"}, "description": {"short": "Real-time meeting transcription for Microsoft Teams", "full": "AccureMD provides real-time transcription capabilities for Microsoft Teams meetings with advanced speech recognition and speaker identification features."}, "accentColor": "#FFFFFF", "bots": [{"botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "scopes": ["personal", "team", "groupChat"], "supportsFiles": false, "isNotificationOnly": false, "supportsCalling": true, "supportsVideo": true}], "composeExtensions": [{"botId": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "commands": [{"id": "startTranscription", "context": ["compose"], "description": "Start meeting transcription", "title": "Start Transcription", "type": "action"}]}], "configurableTabs": [{"configurationUrl": "https://accuremd.azurewebsites.net/config", "canUpdateConfiguration": true, "scopes": ["team", "groupChat"], "context": ["channelTab", "privateChatTab", "meetingChatTab", "meetingDetailsTab", "meetingSidePanel"]}], "staticTabs": [{"entityId": "transcription", "name": "Transcription", "contentUrl": "https://accuremd.azurewebsites.net/transcription", "websiteUrl": "https://accuremd.azurewebsites.net/transcription", "scopes": ["personal"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["accuremd.azurewebsites.net"], "webApplicationInfo": {"id": "24a397f4-16dd-4dae-8b8f-5368c3a81fed", "resource": "api://accuremd.azurewebsites.net/24a397f4-16dd-4dae-8b8f-5368c3a81fed"}}