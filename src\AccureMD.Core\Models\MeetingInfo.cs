using System.ComponentModel.DataAnnotations;

namespace AccureMD.Core.Models;

/// <summary>
/// Represents information about a Microsoft Teams meeting
/// </summary>
public class MeetingInfo
{
    [Required]
    public string MeetingId { get; set; } = string.Empty;
    
    [Required]
    public string JoinUrl { get; set; } = string.Empty;

    public string? CallId { get; set; }

    public string? Subject { get; set; }
    
    public DateTime StartTime { get; set; }
    
    public DateTime? EndTime { get; set; }
    
    public string? OrganizerId { get; set; }
    
    public string? OrganizerName { get; set; }
    
    public List<MeetingParticipant> Participants { get; set; } = new();
    
    public MeetingStatus Status { get; set; } = MeetingStatus.Scheduled;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents a participant in a Teams meeting
/// </summary>
public class MeetingParticipant
{
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    public string? DisplayName { get; set; }
    
    public string? Email { get; set; }
    
    public ParticipantRole Role { get; set; } = ParticipantRole.Attendee;
    
    public DateTime? JoinedAt { get; set; }
    
    public DateTime? LeftAt { get; set; }
    
    public bool IsActive { get; set; }
}

/// <summary>
/// Meeting status enumeration
/// </summary>
public enum MeetingStatus
{
    Scheduled,
    InProgress,
    Completed,
    Cancelled
}

/// <summary>
/// Participant role enumeration
/// </summary>
public enum ParticipantRole
{
    Organizer,
    Presenter,
    Attendee
}
