{"format": 1, "restore": {"D:\\iData Project\\AccureMD\\src\\AccureMD.Core\\AccureMD.Core.csproj": {}}, "projects": {"D:\\iData Project\\AccureMD\\src\\AccureMD.Core\\AccureMD.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\iData Project\\AccureMD\\src\\AccureMD.Core\\AccureMD.Core.csproj", "projectName": "AccureMD.Core", "projectPath": "D:\\iData Project\\AccureMD\\src\\AccureMD.Core\\AccureMD.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\iData Project\\AccureMD\\src\\AccureMD.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Bot.Schema": {"target": "Package", "version": "[4.22.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.42.0, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.3.1, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.3.1, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}